"use client";

import { GoogleIcon, ArrowDown } from "@/assets/icons";
import { AppButton, PasswordInput } from "@/components";
import { errorMsg } from "@/libs/toast";
import rf from "@/services/RequestFactory";
import { useContext, useState } from "react";
import { EStep } from "../steps";
import { handleSocialLogin } from "@/utils/auth";
import { ELoginProvider } from "@/types/auth";
import Link from "next/link";
import { RegisterStepContext } from "../contexts/RegisterStepContext";
import { validatePassword } from "@/utils/password";

type CreatePasswordProps = {
  setStep: (step: EStep) => void;
};

export const CreatePassword: React.FC<CreatePasswordProps> = ({ setStep }) => {
  const { mailOrPhone, goToPreviousStep } = useContext(RegisterStepContext)!;
  const [password, setPassword] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);

  const { isValid: isPasswordValid } = validatePassword(password);

  const handleGoogleLogin = () => {
    handleSocialLogin(ELoginProvider.GOOGLE);
  };

  const onSubmit = async () => {
    if (!password || !isPasswordValid) {
      return;
    }

    try {
      setIsLoading(true);
      const response = await rf.getRequest("AuthRequest").register({
        email: mailOrPhone,
        password,
      });
      if (response?.success) {
        setStep(EStep.VerifyEmail);
      }
      setIsLoading(false);
    } catch (error: any) {
      setIsLoading(false);
      errorMsg(error.message || "Something went wrong!");
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      onSubmit();
    }
  };

  return (
    <>
      <div className="mb-4 flex items-center gap-3">
        <button
          onClick={goToPreviousStep}
          className="text-white-700 hover:text-white-1000 cursor-pointer transition-colors"
          type="button"
          aria-label="Go back to email input"
        >
          <ArrowDown className="rotate-[90deg]" />
        </button>
        <div>
          <div className="heading-lg-semibold-24 lg:text-[32px]">
            Create a password
          </div>
        </div>
      </div>

      <PasswordInput
        value={password}
        onChange={setPassword}
        onKeyDown={handleKeyDown}
        placeholder="Enter the Password"
        autoFocus
        showValidation
        autoComplete="new-password"
      />

      <AppButton
        disabled={!password || !isPasswordValid}
        variant="buy"
        size="large"
        onClick={onSubmit}
        isLoading={isLoading}
      >
        Next
      </AppButton>

      <div className="flex items-center gap-1">
        <div className="bg-white-100 h-[1px] w-[calc(50%-4px)]"></div>
        <div className="body-md-regular-14 text-white-800">Or</div>
        <div className="bg-white-100 h-[1px] w-[calc(50%-4px)]"></div>
      </div>

      <AppButton
        variant="outline"
        size="large"
        className="relative"
        onClick={handleGoogleLogin}
      >
        <GoogleIcon className="absolute left-4" /> Continue with Google
      </AppButton>

      <div className="flex justify-center py-2.5">
        <Link href="/login">
          <div className="body-md-medium-14 cursor-pointer text-green-500">
            Sign In
          </div>
        </Link>
      </div>
    </>
  );
};
