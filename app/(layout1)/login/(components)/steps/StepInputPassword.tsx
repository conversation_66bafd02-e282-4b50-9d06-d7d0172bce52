"use client";

import { GoogleIcon, ArrowDown } from "@/assets/icons";
import { AppButton, PasswordInput } from "@/components";
import { errorMsg, successMsg } from "@/libs/toast";
import rf from "@/services/RequestFactory";
import { ELoginProvider } from "@/types/auth";
import { ErrorStatus } from "@/types/errors";
import { handleSocialLogin, getRedirectPath } from "@/utils/auth";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useContext, useState } from "react";
import { LoginStepContext } from "../contexts/LoginStepContext";
import { ELoginStep } from "../steps";

type StepInputPasswordProps = {
  setStep: (step: ELoginStep) => void;
  mailOrPhone: string;
  password: string;
  setPassword: (password: string) => void;
  setIsNeedVerifyCode: (value: boolean) => void;
};

export const StepInputPassword: React.FC<StepInputPasswordProps> = ({
  setStep,
  mailOrPhone,
  password,
  setPassword,
  setIsNeedVerifyCode,
}) => {
  const [errors, setErrors] = useState({
    password: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { redirectUrl, goToPreviousStep } = useContext(LoginStepContext)!;

  const handleNewUserFlow = async () => {
    const newUserResponse = await rf.getRequest("AuthRequest").checkNewUser();

    if (!newUserResponse.success || !newUserResponse.isNewUser) {
      return false;
    }

    await rf.getRequest("AuthRequest").clearNewUserCookie();
    setStep(ELoginStep.WelcomeAboard);
    return true;
  };

  const handleExistingUserFlow = async () => {
    const finalRedirectPath = getRedirectPath(redirectUrl);

    await router.push(finalRedirectPath);
    await router.refresh();
  };

  const handleMFARequired = (errorMessage: string) => {
    if (!errorMessage?.includes("MFA code is required")) {
      return false;
    }

    setIsNeedVerifyCode(true);
    setStep(ELoginStep.VerifyMFA);
    return true;
  };

  const handleLoginError = (response: any) => {
    const errorMessage =
      response?.response?.data?.error || response.error || "Login error";

    if (response.status !== ErrorStatus.UNAUTHORIZED) {
      errorMsg(errorMessage);
      return;
    }

    if (handleMFARequired(errorMessage)) {
      return;
    }

    setErrors({ password: errorMessage });
  };

  const handleLogin = async () => {
    setIsLoading(true);

    try {
      const response = await rf.getRequest("AuthRequest").login({
        email: mailOrPhone,
        password,
      });

      if (!response?.success) {
        handleLoginError(response);
        return;
      }

      const isNewUser = await handleNewUserFlow();

      if (!isNewUser) {
        await handleExistingUserFlow();
      }

      successMsg("Login Successfully!");
    } catch (error: any) {
      errorMsg(error?.message || "Login error");
    } finally {
      setIsLoading(false);
    }
  };

  const onNext = async () => {
    if (!password) {
      setErrors({
        password: "Please enter your password.",
      });
      return;
    }

    setErrors({
      password: "",
    });
    await handleLogin();
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      onNext();
    }
  };

  const handlePasswordChange = (value: string) => {
    setPassword(value);
    if (errors.password) {
      setErrors({
        password: "",
      });
    }
  };

  const handleClickOutside = () => {
    setErrors({
      password: "",
    });
  };

  return (
    <>
      <div className="mb-4 flex items-center gap-3">
        <button
          onClick={goToPreviousStep}
          className="text-white-700 hover:text-white-1000 cursor-pointer transition-colors"
          type="button"
          aria-label="Go back to email input"
        >
          <ArrowDown className="rotate-[90deg]" />
        </button>
        <div>
          <div className="heading-lg-semibold-24 lg:text-[24px]">
            Enter your password
          </div>
        </div>
      </div>

      <div>
        <PasswordInput
          value={password}
          onChange={handlePasswordChange}
          onKeyDown={handleKeyDown}
          onBlur={handleClickOutside}
          autoFocus
          autoComplete="current-password"
          error={errors?.password}
        />

        <div className="mt-2 flex justify-end">
          <Link href="/forgot-password">
            <div className="body-sm-medium-12 cursor-pointer text-green-500 hover:text-green-400">
              Forgot Password?
            </div>
          </Link>
        </div>
      </div>

      <AppButton
        variant="buy"
        size="large"
        onClick={onNext}
        isLoading={isLoading}
        disabled={!password.trim()}
      >
        Log In
      </AppButton>

      <div className="flex items-center gap-1">
        <div className="bg-white-100 h-[1px] w-[calc(50%-4px)]"></div>
        <div className="body-md-regular-14 text-white-800">Or</div>
        <div className="bg-white-100 h-[1px] w-[calc(50%-4px)]"></div>
      </div>

      <AppButton
        variant="outline"
        size="large"
        className="relative"
        onClick={() => handleSocialLogin(ELoginProvider.GOOGLE, redirectUrl)}
      >
        <GoogleIcon className="absolute left-4" /> Continue with Google
      </AppButton>

      <div className="flex justify-center py-2.5">
        <Link href="/register">
          <div className="body-md-medium-14 cursor-pointer text-green-500">
            Create a VDAX Account
          </div>
        </Link>
      </div>
    </>
  );
};
