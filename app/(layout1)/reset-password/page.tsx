"use client";

import React, { useState, useEffect } from "react";
import { AppButton, PasswordInput } from "@/components";
import { errorMsg, successMsg } from "@/libs/toast";
import rf from "@/services/RequestFactory";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { validatePasswordForm } from "@/utils/password";

export default function ResetPasswordPage() {
  const [code, setCode] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [confirmPassword, setConfirmPassword] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [countdown, setCountdown] = useState<number>(60);
  const [allowResend, setAllowResend] = useState<boolean>(false);
  const [isLoadingResend, setIsLoadingResend] = useState<boolean>(false);
  const [errors, setErrors] = useState({
    code: "",
    password: "",
    confirmPassword: "",
  });

  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Get email and code from URL parameters if available
    const codeParam = searchParams?.get("code");

    if (codeParam) {
      setCode(codeParam);
    }
  }, [searchParams]);

  // Countdown timer effect
  useEffect(() => {
    if (countdown === 0) {
      setAllowResend(true);
      return;
    }

    const intervalId = setInterval(() => {
      setCountdown((prevCountdown) => prevCountdown - 1);
    }, 1000);

    return () => clearInterval(intervalId);
  }, [countdown]);

  // Format countdown time for display
  const formatCountdown = (seconds: number): string => {
    if (seconds <= 60) {
      return `${seconds}s`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  const handleResendCode = async () => {
    const email = searchParams?.get("email");
    if (!email) {
      errorMsg("Email not found. Please go back to forgot password page.");
      return;
    }

    setIsLoadingResend(true);

    try {
      const response = await rf
        .getRequest("AuthRequest")
        .requestForgotPassword(email);

      if (response?.success) {
        successMsg("Verification code has been resent to your email.");
        setCountdown(60);
        setAllowResend(false);
      } else {
        const errorMessage =
          response?.response?.data?.error ||
          response?.error ||
          "Failed to resend verification code.";
        errorMsg(errorMessage);
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Something went wrong. Please try again.";
      errorMsg(errorMessage);
    } finally {
      setIsLoadingResend(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    switch (field) {
      case "code":
        setCode(value);
        break;
      case "password":
        setPassword(value);
        break;
      case "confirmPassword":
        setConfirmPassword(value);
        break;
    }

    // Clear error when user starts typing
    if (errors[field as keyof typeof errors]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors = {
      email: "",
      code: "",
      password: "",
      confirmPassword: "",
    };

    if (!code.trim()) {
      newErrors.code = "Please enter the verification code.";
    } else if (code.length !== 8) {
      newErrors.code = "Verification code must be 8 characters long.";
    }

    if (!password.trim()) {
      newErrors.password = "Please enter a new password.";
    } else if (password.length < 8) {
      newErrors.password = "Password must be at least 8 characters long.";
    }

    if (!confirmPassword.trim()) {
      newErrors.confirmPassword = "Please confirm your password.";
    } else if (password !== confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match.";
    }

    setErrors(newErrors);
    return !Object.values(newErrors).some((error) => error !== "");
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const response = await rf.getRequest("AuthRequest").resetPassword({
        newPassword: password.trim(),
        token: code.trim(),
        confirmPassword: confirmPassword.trim(),
      });

      if (response?.success) {
        successMsg(
          "Password reset successfully! Please log in with your new password."
        );
        router.push("/login");
      } else {
        const errorMessage =
          response?.response?.data?.error ||
          response?.error ||
          "Failed to reset password.";
        errorMsg(errorMessage);
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Something went wrong. Please try again.";
      errorMsg(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSubmit();
    }
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="heading-lg-semibold-24 lg:text-[24px]">
        Reset Password
      </div>

      <div>
        <div className="body-sm-medium-12 text-white-700 mb-2">
          Verification Code
        </div>
        <div className="border-white-100 mb-2 flex gap-2 rounded-[6px] border p-2">
          <input
            className="body-sm-regular-12 placeholder:text-white-300 flex-1 border-0 bg-transparent outline-none"
            placeholder="Enter 8-digit verification code"
            value={code}
            onChange={(e) => handleInputChange("code", e.target.value)}
            onKeyDown={handleKeyDown}
            type="text"
            maxLength={8}
            autoComplete="off"
          />

          <div className="flex gap-2">
            {!allowResend && countdown > 0 && (
              <div className="body-md-regular-14 text-green-500">
                {formatCountdown(countdown)}
              </div>
            )}
            <div
              onClick={() => {
                if (!allowResend || isLoadingResend) return;
                handleResendCode();
              }}
              className={`body-md-regular-14 ${
                allowResend && !isLoadingResend
                  ? "cursor-pointer text-green-500"
                  : "text-white-500 cursor-not-allowed"
              }`}
            >
              {isLoadingResend ? "Sending..." : "Resend Code"}
            </div>
          </div>
        </div>
        {errors?.code && (
          <div className="body-sm-regular-12 text-red-500">{errors?.code}</div>
        )}
      </div>

      <PasswordInput
        value={password}
        onChange={(value) => handleInputChange("password", value)}
        onKeyDown={handleKeyDown}
        placeholder="Enter new password"
        label="New Password"
        error={errors?.password}
        showConfirmPassword
        showValidation
        confirmValue={confirmPassword}
        onConfirmChange={(value) => handleInputChange("confirmPassword", value)}
        confirmPlaceholder="Confirm new password"
        confirmError={errors?.confirmPassword}
        autoComplete="new-password"
      />

      <AppButton
        variant="buy"
        size="large"
        onClick={handleSubmit}
        isLoading={isLoading}
        disabled={!code.trim() || !password.trim() || !confirmPassword.trim()}
      >
        Reset Password
      </AppButton>

      <div className="flex justify-center py-2.5">
        <Link href="/login">
          <div className="body-md-medium-14 cursor-pointer text-green-500">
            Back to Login
          </div>
        </Link>
      </div>
    </div>
  );
}
