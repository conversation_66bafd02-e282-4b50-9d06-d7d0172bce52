"use client";

import React, { useState } from "react";
import { AppButton, PasswordInput } from "@/components";
import { EyeIcon, EyeCloseIcon } from "@/assets/icons";
import { useRouter } from "next/navigation";
import { errorMsg, successMsg } from "@/libs/toast";
import rf from "@/services/RequestFactory";
import { validatePasswordForm } from "@/utils/password";

const FromChangePassword = () => {
  const [currentPassword, setCurrentPassword] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [passwordConfirm, setPasswordConfirm] = useState<string>("");
  const [isShowCurrentPassword, setIsShowCurrentPassword] =
    useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const router = useRouter();

  const validation = validatePasswordForm(password, passwordConfirm);
  const isFormValid = currentPassword.length > 0 && validation.isFormValid;
  const isPasswordsMatch =
    "isPasswordsMatch" in validation ? validation.isPasswordsMatch : false;

  const handleSubmit = async () => {
    if (!isFormValid) {
      if (!currentPassword) {
        errorMsg("Please your current password");
        return;
      }
      if (!isPasswordsMatch) {
        errorMsg("New passwords do not match");
        return;
      }
      return;
    }

    setIsLoading(true);

    try {
      const response = await rf.getRequest("AccountRequest").changePassword({
        currentPassword: currentPassword.trim(),
        newPassword: password.trim(),
        confirmPassword: passwordConfirm.trim(),
      });

      if (response?.success) {
        successMsg("Password changed successfully!");
        router.push("/my/security");
      } else {
        const errorMessage =
          response?.response?.data?.error ||
          response?.error ||
          "Failed to change password.";
        errorMsg(errorMessage);
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Something went wrong. Please try again.";
      errorMsg(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSubmit();
    }
  };

  return (
    <div className="mx-auto flex w-full max-w-[443px] flex-1 flex-col items-center justify-center lg:mt-[100px]">
      <div className="heading-lg-medium-24 mb-4 text-center">
        Change your password
      </div>

      <div className="mt-8 w-full">
        <div className="body-sm-medium-12 text-white-700 mb-2">
          Current Password
        </div>
        <div className="border-white-100 flex w-full items-center gap-2 rounded-[6px] border p-2">
          <input
            type={isShowCurrentPassword ? "text" : "password"}
            className="body-sm-regular-12 placeholder:text-white-300 flex-1 outline-none"
            value={currentPassword}
            onChange={(e) => setCurrentPassword(e.target.value)}
            onKeyDown={handleKeyDown}
            disabled={isLoading}
          />
          <div
            className="cursor-pointer"
            onClick={() => setIsShowCurrentPassword(!isShowCurrentPassword)}
          >
            {isShowCurrentPassword ? <EyeCloseIcon /> : <EyeIcon />}
          </div>
        </div>
      </div>

      <PasswordInput
        value={password}
        onChange={setPassword}
        onKeyDown={handleKeyDown}
        disabled={isLoading}
        label="New Password"
        showValidation
        showConfirmPassword
        confirmValue={passwordConfirm}
        onConfirmChange={setPasswordConfirm}
        confirmError={
          passwordConfirm.length > 0 && !isPasswordsMatch
            ? "New passwords do not match"
            : undefined
        }
        autoComplete="new-password"
        className="mt-8 w-full"
      />

      <AppButton
        size="large"
        variant="buy"
        className="mt-8 w-full"
        onClick={handleSubmit}
        disabled={!isFormValid || isLoading}
        isLoading={isLoading}
      >
        Confirm
      </AppButton>
    </div>
  );
};

const ChangePasswordPage = () => {
  const [isShowModalEnterCode, setIsShowModalEnterCode] =
    useState<boolean>(false);
  const [isShowChangePassword, setIsShowChangePassword] =
    useState<boolean>(true);
  const router = useRouter();

  // useEffect(() => {
  //   setIsShowModalEnterCode(true);
  // }, []);

  return (
    <div className="mt-4">
      <div className="heading-lg-medium-24 mb-4 hidden md:block">
        Change Password
      </div>

      {isShowChangePassword && <FromChangePassword />}

      {/* {isShowModalEnterCode && (
        <ModalEnterVerificationCode
          isOpen={isShowModalEnterCode}
          onClose={() => {
            setIsShowModalEnterCode(false);
            router.push("/my/security");
          }}
          onNext={() => {
            setIsShowModalEnterCode(false);
            setIsShowChangePassword(true);
          }}
        />
      )} */}
    </div>
  );
};

export default ChangePasswordPage;
