"use client";

import React, { useEffect, useState } from "react";
import { ModalEnterVerificationCode } from "@/modals";
import { AppButton, PasswordInput } from "@/components";

import { useRouter } from "next/navigation";
import { errorMsg, successMsg } from "@/libs/toast";
import rf from "@/services/RequestFactory";
import { validatePasswordForm } from "@/utils/password";

const FromSetPassword = () => {
  const [password, setPassword] = useState<string>("");
  const [passwordConfirm, setPasswordConfirm] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const router = useRouter();

  const validation = validatePasswordForm(password, passwordConfirm);
  const isFormValid = validation.isFormValid;
  const isPasswordsMatch =
    "isPasswordsMatch" in validation ? validation.isPasswordsMatch : false;

  const handleSubmit = async () => {
    if (!isFormValid) {
      if (!isPasswordsMatch) {
        errorMsg("Passwords do not match");
      }
      return;
    }

    setIsLoading(true);

    try {
      const response = await rf.getRequest("AccountRequest").setPassword({
        password: password.trim(),
        confirmPassword: passwordConfirm.trim(),
      });

      if (response?.success) {
        successMsg("Password set successfully!");
        router.push("/my/security");
      } else {
        const errorMessage =
          response?.response?.data?.error ||
          response?.error ||
          "Failed to set password.";
        errorMsg(errorMessage);
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Something went wrong. Please try again.";
      errorMsg(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSubmit();
    }
  };

  return (
    <div className="mx-auto flex w-full max-w-[443px] flex-1 flex-col items-center justify-center lg:mt-[50px]">
      <div className="heading-lg-medium-24 mb-4 text-center">
        Set your password
      </div>

      <PasswordInput
        value={password}
        onChange={setPassword}
        onKeyDown={handleKeyDown}
        disabled={isLoading}
        label="New Password"
        showValidation
        showConfirmPassword
        confirmValue={passwordConfirm}
        onConfirmChange={setPasswordConfirm}
        confirmError={
          passwordConfirm.length > 0 && !isPasswordsMatch
            ? "Passwords do not match"
            : undefined
        }
        autoComplete="new-password"
        className="mt-8 w-full"
      />

      <AppButton
        size="large"
        variant="buy"
        className="mt-8 w-full"
        onClick={handleSubmit}
        disabled={!isFormValid || isLoading}
        isLoading={isLoading}
      >
        Confirm
      </AppButton>
    </div>
  );
};

const SetPasswordPage = () => {
  const [isShowModalEnterCode, setIsShowModalEnterCode] =
    useState<boolean>(false);
  const [isShowChangePassword, setIsShowChangePassword] =
    useState<boolean>(true);
  const router = useRouter();

  useEffect(() => {
    setIsShowModalEnterCode(false);
  }, []);

  return (
    <div className="mt-4">
      <div className="heading-lg-medium-24 mb-4 hidden md:block">
        Set Password
      </div>

      {isShowChangePassword && <FromSetPassword />}

      {isShowModalEnterCode && (
        <ModalEnterVerificationCode
          isOpen={isShowModalEnterCode}
          onClose={() => {
            setIsShowModalEnterCode(false);
            router.push("/my/security");
          }}
          onNext={() => {
            setIsShowModalEnterCode(false);
            setIsShowChangePassword(true);
          }}
        />
      )}
    </div>
  );
};

export default SetPasswordPage;
