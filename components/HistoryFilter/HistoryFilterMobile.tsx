"use client";

import React from "react";
import {
  HistoryFilterActions,
  HistoryFilterState,
} from "@/hooks/useHistoryFilter";
import {
  ButtonSelectOrderSideMobile,
  ButtonSelectSortByMobile,
  ButtonSelectQuoteMobile,
  ButtonSelectBaseMobile,
} from "../OrderExchange/components";
import { ButtonFilterByTime } from "../OrderExchange/components";

interface HistoryFilterMobileProps {
  filterActions: HistoryFilterActions;
  filterState: HistoryFilterState;
  baseAssetOptions: any[];
  quoteAssetOptions: any[];
}

export const HistoryFilterMobile = ({
  filterActions,
  filterState,
  baseAssetOptions,
  quoteAssetOptions,
}: HistoryFilterMobileProps) => {
  return (
    <div className="flex items-center justify-between px-4 py-3 lg:hidden">
      <div className="flex items-center gap-2">
        <ButtonSelectBaseMobile
          setBase={filterActions.setBase}
          base={filterState.base}
          options={baseAssetOptions}
        />
        <ButtonSelectQuoteMobile
          setQuote={filterActions.setQuote}
          quote={filterState.quote}
          options={quoteAssetOptions}
        />
        <ButtonSelectOrderSideMobile
          setOrderSide={filterActions.setSide}
          side={filterState.side}
        />
        <ButtonSelectSortByMobile
          setSortBy={filterActions.setSortBy}
          sortBy={filterState.sortBy}
        />
      </div>

      <div className="flex items-center gap-4">
        <div
          className="body-xs-medium-10"
          onClick={filterActions.handleResetFilters}
        >
          Reset
        </div>

        <ButtonFilterByTime
          filterActions={filterActions}
          filterState={filterState}
        />
      </div>
    </div>
  );
};
