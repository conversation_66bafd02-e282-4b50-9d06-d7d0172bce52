import { ChevronDownIcon } from "@/assets/icons";
import { AppButton } from "../../AppButton";
import React, { useState } from "react";
import { ModalSelectTypeOrder } from "@/modals/ModalSelectTypeOrder";
import { EOrderTypeParam } from "./SelectOrderType";

export const ButtonSelectTypeOrderMobile = ({
  options,
  orderType,
  setOrderType,
}: {
  options: any[];
  setOrderType: (value: EOrderTypeParam) => void;
  orderType: EOrderTypeParam;
}) => {
  const [isShow, setIsShow] = useState<boolean>(false);
  return (
    <>
      <AppButton
        onClick={() => setIsShow(true)}
        variant="secondary"
        size="small"
        className="items-center gap-1 px-2 !text-[10px]"
      >
        Type <ChevronDownIcon />
      </AppButton>

      {isShow && (
        <ModalSelectTypeOrder
          orderType={orderType}
          setOrderType={setOrderType}
          options={options}
          isOpen={isShow}
          onClose={() => setIsShow(false)}
        />
      )}
    </>
  );
};
