import { ChevronDownIcon } from "@/assets/icons";
import { AppButton } from "../../AppButton";
import React, { useState } from "react";
import { ModalSelectSideOrder } from "@/modals/ModalSelectSideOrder";

export const ButtonSelectOrderSideMobile = ({
  side,
  setOrderSide,
}: {
  setOrderSide: (value: string) => void;
  side: string;
}) => {
  const [isShow, setIsShow] = useState<boolean>(false);
  return (
    <>
      <AppButton
        onClick={() => setIsShow(true)}
        variant="secondary"
        size="small"
        className="items-center gap-1 px-2 !text-[10px]"
      >
        Side <ChevronDownIcon />
      </AppButton>

      {isShow && (
        <ModalSelectSideOrder
          orderSide={side}
          setOrderSide={setOrderSide}
          isOpen={isShow}
          onClose={() => setIsShow(false)}
        />
      )}
    </>
  );
};
