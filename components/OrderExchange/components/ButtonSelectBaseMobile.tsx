import { ChevronDownIcon } from "@/assets/icons";
import { AppButton } from "../../AppButton";
import React, { useState } from "react";
import { ModalSelectBaseOrQuote } from "@/modals";

export const ButtonSelectBaseMobile = ({
  base,
  setBase,
  options,
}: {
  setBase: (value: string) => void;
  base: string;
  options: any[];
}) => {
  const [isShow, setIsShow] = useState<boolean>(false);
  return (
    <>
      <AppButton
        onClick={() => setIsShow(true)}
        variant="secondary"
        size="small"
        className="items-center gap-1 px-2 !text-[10px]"
      >
        Base <ChevronDownIcon />
      </AppButton>

      {isShow && (
        <ModalSelectBaseOrQuote
          value={base}
          isBase
          setValue={setBase}
          options={options}
          isOpen={isShow}
          onClose={() => setIsShow(false)}
        />
      )}
    </>
  );
};
