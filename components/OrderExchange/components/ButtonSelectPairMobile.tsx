import { ChevronDownIcon } from "@/assets/icons";
import { AppButton } from "../../AppButton";
import React, { useState } from "react";
import { ModalSelectPair } from "@/modals/ModalSelectPair";

export const ButtonSelectPairMobile = ({
  pair,
  setPair,
  options,
}: {
  setPair: (value: string) => void;
  pair: string;
  options: any[];
}) => {
  const [isShow, setIsShow] = useState<boolean>(false);
  return (
    <>
      <AppButton
        onClick={() => setIsShow(true)}
        variant="secondary"
        size="small"
        className="items-center gap-1 px-2 !text-[10px]"
      >
        Pair <ChevronDownIcon />
      </AppButton>

      {isShow && (
        <ModalSelectPair
          pair={pair}
          setPair={setPair}
          options={options}
          isOpen={isShow}
          onClose={() => setIsShow(false)}
        />
      )}
    </>
  );
};
