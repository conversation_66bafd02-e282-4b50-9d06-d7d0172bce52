import { ChevronDownIcon } from "@/assets/icons";
import { AppButton } from "../../AppButton";
import React, { useState } from "react";
import { ModalSelectBaseOrQuote } from "@/modals";

export const ButtonSelectQuoteMobile = ({
  quote,
  setQuote,
  options,
}: {
  setQuote: (value: string) => void;
  quote: string;
  options: any[];
}) => {
  const [isShow, setIsShow] = useState<boolean>(false);
  return (
    <>
      <AppButton
        onClick={() => setIsShow(true)}
        variant="secondary"
        size="small"
        className="items-center gap-1 px-2 !text-[10px]"
      >
        Quote <ChevronDownIcon />
      </AppButton>

      {isShow && (
        <ModalSelectBaseOrQuote
          value={quote}
          setValue={setQuote}
          options={options}
          isOpen={isShow}
          onClose={() => setIsShow(false)}
        />
      )}
    </>
  );
};
