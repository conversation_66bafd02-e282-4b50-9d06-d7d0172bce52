import { ChevronDownIcon } from "@/assets/icons";
import { AppButton } from "../../AppButton";
import React, { useState } from "react";
import { ModalSelectSortBy } from "@/modals/ModalSelectSortBy";

export const ButtonSelectSortByMobile = ({
  sortBy,
  setSortBy,
}: {
  setSortBy: (value: string) => void;
  sortBy: string;
}) => {
  const [isShow, setIsShow] = useState<boolean>(false);
  return (
    <>
      <AppButton
        onClick={() => setIsShow(true)}
        variant="secondary"
        size="small"
        className="items-center gap-1 px-2 !text-[10px]"
      >
        Sort by <ChevronDownIcon />
      </AppButton>

      {isShow && (
        <ModalSelectSortBy
          sortBy={sortBy}
          setSortBy={setSortBy}
          isOpen={isShow}
          onClose={() => setIsShow(false)}
        />
      )}
    </>
  );
};
