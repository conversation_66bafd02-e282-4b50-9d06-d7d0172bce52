import { FilterIcon } from "@/assets/icons";
import React, { useState } from "react";
import { ModalFilterByTime } from "@/modals";
import {
  HistoryFilterActions,
  HistoryFilterState,
} from "@/hooks/useHistoryFilter";

export const ButtonFilterByTime = ({
  filterActions,
  filterState,
}: {
  filterActions: HistoryFilterActions;
  filterState: HistoryFilterState;
}) => {
  const [isShow, setIsShow] = useState<boolean>(false);
  return (
    <>
      <div onClick={() => setIsShow(true)}>
        <FilterIcon />
      </div>

      {isShow && (
        <ModalFilterByTime
          resolution={filterState.resolution}
          setResolution={filterActions.setResolution}
          startDate={filterState.startDate}
          endDate={filterState.endDate}
          setDateRange={filterActions.setDateRange}
          isOpen={isShow}
          onClose={() => setIsShow(false)}
        />
      )}
    </>
  );
};
