"use client";

import React, { useState, forwardRef, useImperative<PERSON><PERSON><PERSON> } from "react";
import { EyeIcon, EyeCloseIcon, CheckIcon } from "@/assets/icons";
import { validatePassword, validatePasswordMatch } from "@/utils/password";
import clsx from "clsx";

export interface PasswordInputProps {
  // Basic input props
  value: string;
  onChange: (value: string) => void;
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  onBlur?: () => void;
  placeholder?: string;
  autoFocus?: boolean;
  disabled?: boolean;
  autoComplete?: string;

  // Validation and display
  label?: string;
  error?: string;
  showValidation?: boolean;

  // Confirm password functionality
  showConfirmPassword?: boolean;
  confirmValue?: string;
  onConfirmChange?: (value: string) => void;
  confirmLabel?: string;
  confirmError?: string;
  confirmPlaceholder?: string;

  // Styling
  className?: string;
  inputClassName?: string;
}

export interface PasswordInputRef {
  focus: () => void;
  validate: () => boolean;
}

const PasswordInput = forwardRef<PasswordInputRef, PasswordInputProps>(
  (props, ref) => {
    const {
      value,
      onChange,
      onKeyDown,
      onBlur,
      placeholder = "Enter password",
      autoFocus = false,
      disabled = false,
      autoComplete = "current-password",
      label = "Password",
      error,
      showValidation = false,
      showConfirmPassword = false,
      confirmValue = "",
      onConfirmChange,
      confirmLabel = "Confirm Password",
      confirmError,
      confirmPlaceholder = "Confirm password",
      className,
      inputClassName,
    } = props;

    const [isShowPassword, setIsShowPassword] = useState(false);
    const [isShowConfirmPassword, setIsShowConfirmPassword] = useState(false);
    const passwordInputRef = React.useRef<HTMLInputElement>(null);
    const confirmInputRef = React.useRef<HTMLInputElement>(null);

    // Validation
    const passwordValidation = validatePassword(value);
    const { isValidLength, isValidHasNumber, isValidHasUppercase } =
      passwordValidation;

    const matchValidation =
      showConfirmPassword && confirmValue !== undefined
        ? validatePasswordMatch(value, confirmValue)
        : null;

    useImperativeHandle(ref, () => ({
      focus: () => {
        passwordInputRef.current?.focus();
      },
      validate: () => {
        if (showConfirmPassword) {
          return (
            passwordValidation.isValid &&
            (matchValidation?.isPasswordsMatch ?? false)
          );
        }
        return passwordValidation.isValid;
      },
    }));

    const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      onChange(e.target.value);
    };

    const handleConfirmPasswordChange = (
      e: React.ChangeEvent<HTMLInputElement>
    ) => {
      if (onConfirmChange) {
        onConfirmChange(e.target.value);
      }
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (onKeyDown) {
        onKeyDown(e);
      }
    };

    return (
      <div className={className}>
        {/* Main Password Input */}
        <div>
          <div className="body-sm-medium-12 text-white-700 mb-2">{label}</div>
          <div className="border-white-100 flex w-full items-center gap-2 rounded-[6px] border px-2 py-3">
            <input
              ref={passwordInputRef}
              type={isShowPassword ? "text" : "password"}
              className={clsx(
                "body-sm-regular-12 placeholder:text-white-300 flex-1 outline-none",
                inputClassName
              )}
              placeholder={placeholder}
              value={value}
              autoFocus={autoFocus}
              onKeyDown={handleKeyDown}
              onBlur={onBlur}
              onChange={handlePasswordChange}
              disabled={disabled}
              autoComplete={autoComplete}
            />
            <div
              onClick={() => setIsShowPassword(!isShowPassword)}
              className="cursor-pointer"
            >
              {isShowPassword ? <EyeIcon /> : <EyeCloseIcon />}
            </div>
          </div>
          {error && (
            <div className="body-sm-regular-12 mt-1 text-red-500">{error}</div>
          )}
        </div>

        {/* Password Validation Indicators */}
        {showValidation && (
          <div className="mt-4 flex flex-col gap-2">
            <div
              className={`flex items-center gap-2 ${
                isValidLength ? "text-brand-500" : "text-white-500"
              }`}
            >
              <CheckIcon />
              <div className="body-md-regular-14">8 to 128 characters</div>
            </div>
            <div
              className={`flex items-center gap-2 ${
                isValidHasNumber ? "text-brand-500" : "text-white-500"
              }`}
            >
              <CheckIcon />
              <div className="body-md-regular-14">At least 1 number</div>
            </div>
            <div
              className={`flex items-center gap-2 ${
                isValidHasUppercase ? "text-brand-500" : "text-white-500"
              }`}
            >
              <CheckIcon />
              <div className="body-md-regular-14">
                At least 1 upper case letter
              </div>
            </div>
          </div>
        )}

        {showConfirmPassword && (
          <div className="mt-8">
            <div className="body-sm-medium-12 text-white-700 mb-2">
              {confirmLabel}
            </div>
            <div className="border-white-100 flex w-full items-center gap-2 rounded-[6px] border px-2 py-3">
              <input
                ref={confirmInputRef}
                type={isShowConfirmPassword ? "text" : "password"}
                className={clsx(
                  "body-sm-regular-12 placeholder:text-white-300 flex-1 outline-none",
                  inputClassName
                )}
                placeholder={confirmPlaceholder}
                value={confirmValue}
                onKeyDown={handleKeyDown}
                onChange={handleConfirmPasswordChange}
                disabled={disabled}
                autoComplete="new-password"
              />
              <div
                onClick={() => setIsShowConfirmPassword(!isShowConfirmPassword)}
                className="cursor-pointer"
              >
                {isShowConfirmPassword ? <EyeIcon /> : <EyeCloseIcon />}
              </div>
            </div>
            {confirmError && (
              <div className="body-sm-regular-12 mt-1 text-red-500">
                {confirmError}
              </div>
            )}
          </div>
        )}
      </div>
    );
  }
);

PasswordInput.displayName = "PasswordInput";

export default PasswordInput;
