import React, { useEffect, useMemo, useRef, useState } from "react";
import {
  ChevronDownIcon,
  CopyIcon,
  LinkAngledIcon,
  CheckboxCheckedIcon,
  CheckboxIcon,
  InforIcon,
} from "@/assets/icons";
import { AppDataTableRealtime } from "@/components";
import { copyToClipboard } from "@/utils/helper";
import { useMediaQuery } from "react-responsive";
import rf from "@/services/RequestFactory";
import { TWithdrawTransaction } from "../types/transaction";
import {
  formatNumber,
  formatShortAddress,
  formatUnixTimestamp,
} from "../utils/format";
import { useSelector } from "react-redux";
import { RootState } from "../store/index";
import { TNetwork } from "../types/network";
import { BROADCAST_EVENTS } from "../libs/broadcast";
import Link from "next/link";

const EColorStatus = {
  pending: "orange",
  processing: "orange",
  completed: "green",
  failed: "red",
  canceled: "red",
  rejected: "red",
};

const WithdrawItem = ({
  withdrawTx,
  type,
}: {
  withdrawTx: TWithdrawTransaction;
  type: string;
}) => {
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
  const networks = useSelector((state: RootState) => state.metadata.networks);
  const assets = useSelector((state: RootState) => state.metadata.assets);

  const networkWithdraw = networks.find(
    (network: TNetwork) => network.id === withdrawTx.networkId
  );

  const assetData = useMemo(
    () => assets.find((item) => item.symbol === withdrawTx?.asset),
    [assets, withdrawTx]
  );

  if (type === EType.VDAX_USER && withdrawTx?.isExternal) return <></>;
  if (type === EType.ADDRESS && !withdrawTx?.isExternal) return <></>;

  if (isMobile) {
    return (
      <div className="border-white-50 flex flex-col gap-4 border-b py-2">
        <div className="flex justify-between">
          <div className="body-md-medium-14 flex items-center gap-2">
            <img
              src={assetData?.logoUrl}
              alt={withdrawTx.asset}
              width={20}
              height={20}
              className="aspect-square h-[20px] w-[20px] rounded-full"
            />
            {withdrawTx.asset}
          </div>
          <div className="body-md-medium-14">
            {formatNumber(withdrawTx.amount)} {withdrawTx.asset}
          </div>
        </div>
        <div className="flex justify-between">
          <div className="body-md-regular-14 text-white-500">Status</div>
          <div
            className={`body-md-regular-14 capitalize text-${
              EColorStatus[
                withdrawTx.status?.toLowerCase() as keyof typeof EColorStatus
              ]
            }-500`}
          >
            {withdrawTx.status?.toLowerCase()}
          </div>
        </div>
        <div className="flex justify-between">
          <div className="body-md-regular-14 text-white-500">Time</div>
          <div className="body-md-regular-14">
            {formatUnixTimestamp(withdrawTx.createdAt, "YYYY-MM-DD HH:mm")}
          </div>
        </div>

        {type === EType.ADDRESS ? (
          <>
            <div className="flex justify-between">
              <div className="body-md-regular-14 text-white-500">Network</div>
              <div className="body-md-regular-14">
                {networkWithdraw?.symbol}
              </div>
            </div>
            <div className="flex justify-between">
              <div className="body-md-regular-14 text-white-500">Address</div>
              <div className="body-md-regular-14">
                <div className="body-sm-regular-12 flex items-center gap-2.5">
                  <div>{formatShortAddress(withdrawTx.address)}</div>
                  <a
                    href={`${networkWithdraw?.explorerUrl}/address/${withdrawTx.address}`}
                    target="_blank"
                  >
                    <LinkAngledIcon className="cursor-pointer" />
                  </a>
                  <CopyIcon
                    className="cursor-pointer"
                    onClick={() => copyToClipboard(withdrawTx.address)}
                  />
                </div>
              </div>
            </div>
            <div className="flex justify-between">
              <div className="body-md-regular-14 text-white-500">
                Transaction ID (TxID)
              </div>
              <div className="body-md-regular-14">
                <div className="body-sm-regular-12 flex items-center gap-2.5">
                  {withdrawTx.txHash ? (
                    <>
                      <div>{formatShortAddress(withdrawTx.txHash)}</div>
                      <a
                        href={`${networkWithdraw?.explorerUrl}/tx/${withdrawTx.txHash}`}
                        target="_blank"
                      >
                        <LinkAngledIcon className="cursor-pointer" />
                      </a>
                      <CopyIcon
                        className="cursor-pointer"
                        onClick={() => copyToClipboard(withdrawTx.txHash)}
                      />
                    </>
                  ) : (
                    "--"
                  )}
                </div>
              </div>
            </div>
          </>
        ) : (
          <div className="flex justify-between">
            <div className="body-md-regular-14 text-white-500">Send To</div>
            {withdrawTx?.toUserId ? (
              <div className="body-md-regular-14 flex items-center gap-2">
                {withdrawTx?.toUserId}{" "}
                <CopyIcon
                  className="cursor-pointer"
                  onClick={() =>
                    copyToClipboard(withdrawTx?.toUserId?.toString() || "")
                  }
                />
              </div>
            ) : (
              "--"
            )}
          </div>
        )}
      </div>
    );
  }

  if (type === EType.VDAX_USER) {
    return (
      <div className="border-white-50 hover:bg-white-50 flex w-full items-center lg:border-b">
        <div className="body-sm-regular-12 flex w-[25%] items-center gap-2 px-2 py-2.5 text-left lg:min-w-[184px]">
          <img
            src={assetData?.logoUrl}
            alt={withdrawTx.asset}
            width={24}
            height={24}
            className="aspect-square h-[24px] w-[24px] rounded-full"
          />
          <div>
            <div className="body-sm-regular-12">{withdrawTx.asset}</div>
            <div className="body-xs-regular-10 text-white-500">
              {formatUnixTimestamp(withdrawTx.createdAt, "YYYY-MM-DD HH:mm")}
            </div>
          </div>
        </div>
        <div className="body-sm-regular-12 w-[25%] px-2 py-2.5 text-right lg:min-w-[100px]">
          {formatNumber(withdrawTx.amount)}
        </div>
        <div className="body-sm-regular-12 flex w-[25%] items-center justify-end gap-2 px-2 py-2.5 lg:min-w-[100px]">
          {withdrawTx?.toUserId ? (
            <>
              {withdrawTx?.toUserId}{" "}
              <CopyIcon
                className="cursor-pointer"
                onClick={() =>
                  copyToClipboard(withdrawTx?.toUserId?.toString() || "")
                }
              />
            </>
          ) : (
            "--"
          )}
        </div>
        <div
          className={`body-sm-regular-12 w-[25%] px-2 py-2.5 text-right capitalize text-${
            EColorStatus[
              withdrawTx.status?.toLowerCase() as keyof typeof EColorStatus
            ]
          }-500 lg:min-w-[150px]`}
        >
          {withdrawTx.status?.toLowerCase()}
        </div>
      </div>
    );
  }

  return (
    <div className="border-white-50 hover:bg-white-50 flex w-full items-center lg:border-b">
      <div className="body-sm-regular-12 flex w-[20%] items-center gap-2 px-2 py-2.5 text-left lg:min-w-[184px]">
        <img
          src={assetData?.logoUrl}
          alt={withdrawTx.asset}
          width={24}
          height={24}
          className="aspect-square h-[24px] w-[24px] rounded-full"
        />
        <div>
          <div className="body-sm-regular-12">{withdrawTx.asset}</div>
          <div className="body-xs-regular-10 text-white-500">
            {formatUnixTimestamp(withdrawTx.createdAt, "YYYY-MM-DD HH:mm")}
          </div>
        </div>
      </div>
      <div className="body-sm-regular-12 w-[15%] px-2 py-2.5 text-right lg:min-w-[100px]">
        {formatNumber(withdrawTx.amount)}
      </div>
      <div className="body-sm-regular-12 w-[10%] px-2 py-2.5 text-right lg:min-w-[100px]">
        {networkWithdraw?.symbol}
      </div>
      <div className="flex justify-end px-2 py-2.5 md:min-w-[184px] lg:w-[20%]">
        <div className="body-sm-regular-12 flex items-center gap-2.5">
          <div>{formatShortAddress(withdrawTx.address)}</div>
          <a
            href={`${networkWithdraw?.explorerUrl}/address/${withdrawTx.address}`}
            target="_blank"
          >
            <LinkAngledIcon className="cursor-pointer" />
          </a>
          <CopyIcon
            className="cursor-pointer"
            onClick={() => copyToClipboard(withdrawTx.address)}
          />
        </div>
      </div>
      <div className="flex justify-end px-2 py-2.5 md:min-w-[184px] lg:w-[20%] ">
        <div className="body-sm-regular-12 flex items-center gap-2.5">
          {withdrawTx.txHash ? (
            <>
              <div>{formatShortAddress(withdrawTx.txHash)}</div>
              <a
                href={`${networkWithdraw?.explorerUrl}/tx/${withdrawTx.txHash}`}
                target="_blank"
              >
                <LinkAngledIcon className="cursor-pointer" />
              </a>
              <CopyIcon
                className="cursor-pointer"
                onClick={() => copyToClipboard(withdrawTx.txHash)}
              />
            </>
          ) : (
            "--"
          )}
        </div>
      </div>
      <div
        className={`body-sm-regular-12 w-[15%] px-2 py-2.5 text-right capitalize text-${
          EColorStatus[
            withdrawTx.status?.toLowerCase() as keyof typeof EColorStatus
          ]
        }-500 lg:min-w-[150px]`}
      >
        {withdrawTx.status?.toLowerCase()}
      </div>
    </div>
  );
};

enum EType {
  ADDRESS = "ADDRESS",
  VDAX_USER = "VDAX_USER",
}

export const TableRecentWithdraw = () => {
  const [isHideErrorNotice, setIsHideErrorNotice] = useState<boolean>(false);
  const [type, setType] = useState<string>(EType.ADDRESS);
  const dataTableRef = useRef<HTMLDivElement | null>(null);
  const [isMounted, setIsMounted] = useState<boolean>(false);
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  const getData = async (params: any) => {
    try {
      const result = await rf
        .getRequest("AccountRequest")
        .getWithdrawTransactions({
          ...params,
          isExternal: type === EType.ADDRESS,
        });
      return {
        cursor: result?.cursor,
        data: result?.docs || [],
      };
    } catch (err) {
      console.log(err, "getDataWithdrawTx Error");
      return { cursor: null, data: [] };
    }
  };

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) return <></>;

  return (
    <div className="border-white-100 p-4 lg:rounded-[16px] lg:border">
      <div className="border-white-100 border-b pb-4 lg:border-0 lg:pb-0">
        <div className="mb-2 flex items-center justify-between">
          <div className="lg:text-white-500 heading-sm-medium-16">
            Recent Withdrawals
          </div>
          <div className="flex items-center gap-4">
            <div
              onClick={() => setIsHideErrorNotice(!isHideErrorNotice)}
              className="hidden cursor-pointer items-center gap-2 lg:flex "
            >
              <div>
                {isHideErrorNotice ? <CheckboxCheckedIcon /> : <CheckboxIcon />}
              </div>
              <div className="body-md-regular-14">Hide error notices</div>
            </div>
            <Link
              href={
                type === EType.VDAX_USER
                  ? "/my/payment-history"
                  : "/my/transaction-history"
              }
            >
              <div className="body-sm-medium-12 flex cursor-pointer items-center gap-2">
                More <ChevronDownIcon className="rotate-[-90deg]" />
              </div>
            </Link>
          </div>
        </div>

        <div className="flex gap-2">
          <div
            onClick={() => setType(EType.ADDRESS)}
            className={`body-xs-medium-10 cursor-pointer rounded-[4px] px-2 py-1 ${
              type === EType.ADDRESS ? "bg-white-100" : ""
            }`}
          >
            Address
          </div>
          <div
            onClick={() => setType(EType.VDAX_USER)}
            className={`body-xs-medium-10 cursor-pointer rounded-[4px] px-2 py-1 ${
              type === EType.VDAX_USER ? "bg-white-100" : ""
            }`}
          >
            VDAX User
          </div>
        </div>
      </div>

      <div className="w-full">
        <AppDataTableRealtime
          minWidth={1300}
          ref={dataTableRef}
          getData={getData as any}
          shouldAutoFetchOnInit
          handleAddNewItem={{
            broadcastName: BROADCAST_EVENTS.NEW_TRANSACTION,
            fieldKey: "id",
            formatter: (data: any) => {
              const newData = JSON.parse(data);
              if (newData?.transactionType !== "Withdraw") return null;
              return {
                address: newData.address,
                amount: newData.amount,
                asset: newData.asset,
                createdAt: newData.timestamp,
                fee: newData.fee,
                id: newData.id,
                networkId: newData?.networkId,
                status: newData?.status || "PENDING",
                txHash: newData?.txHash,
                toUserId: newData?.toUserId,
                updatedAt: newData?.timestamp,
                requestedAt: newData?.timestamp,
                userId: newData?.userId,
                isExternal: newData?.isExternal,
              };
            },
          }}
          handleUpdateItem={[
            {
              broadcastName: BROADCAST_EVENTS.UPDATED_TRANSACTION,
              fieldKey: "id",
              formatter: (data: any) => {
                const newData = JSON.parse(data);
                if (newData?.transactionType !== "Withdraw") return null;
                return newData;
              },
              update: (oldData: any, newData: any) => {
                return {
                  ...oldData,
                  status: newData?.status,
                  txHash: newData?.txHash,
                  createdAt: newData.timestamp,
                  toUserId: newData?.toUserId,
                };
              },
            },
          ]}
          overrideBodyClassName="w-full"
          renderHeader={() => {
            if (isMobile) return null;
            if (type === EType.ADDRESS) {
              return (
                <>
                  <div className="flex w-full items-center">
                    <div className="body-sm-regular-12 text-white-500 flex w-[20%] items-center px-2 py-1.5 md:min-w-[184px] ">
                      Asset/Date
                    </div>
                    <div className="body-sm-regular-12 text-white-500 w-[15%] px-2 py-1.5 text-right md:min-w-[100px] ">
                      Amount
                    </div>
                    <div className="body-sm-regular-12 text-white-500 w-[10%] px-2 py-1.5 text-right md:min-w-[100px]">
                      Network
                    </div>
                    <div className="body-sm-regular-12 text-white-500 w-[20%] px-2 py-1.5 text-right md:min-w-[184px]">
                      Address
                    </div>
                    <div className="body-sm-regular-12 text-white-500 w-[20%] px-2 py-1.5 text-right md:min-w-[184px]">
                      TxID
                    </div>
                    <div className="body-sm-regular-12 text-white-500 w-[15%] px-2 py-1.5 text-right md:min-w-[150px]">
                      Status
                    </div>
                  </div>
                </>
              );
            }
            return (
              <>
                <div className="flex w-full items-center">
                  <div className="body-sm-regular-12 text-white-500 flex w-[25%] items-center px-2 py-1.5 md:min-w-[184px] ">
                    Asset/Date
                  </div>
                  <div className="body-sm-regular-12 text-white-500 w-[25%] px-2 py-1.5 text-right md:min-w-[100px] ">
                    Amount
                  </div>
                  <div className="body-sm-regular-12 text-white-500 w-[25%] px-2 py-1.5 text-right md:min-w-[100px]">
                    Send To
                  </div>
                  <div className="body-sm-regular-12 text-white-500 w-[25%] px-2 py-1.5 text-right md:min-w-[150px]">
                    Status
                  </div>
                </div>
              </>
            );
          }}
          renderRow={(item: TWithdrawTransaction, index: number) => {
            return <WithdrawItem key={index} withdrawTx={item} type={type} />;
          }}
          height={450}
        />
      </div>
    </div>
  );
};
