import React, { useEffect, useMemo, useRef, useState } from "react";
import {
  ChevronDownIcon,
  CopyIcon,
  LinkAngledIcon,
  InforIcon,
} from "@/assets/icons";
import { AppDataTableRealtime } from "@/components";
import { copyToClipboard } from "@/utils/helper";
import { useMediaQuery } from "react-responsive";
import rf from "@/services/RequestFactory";
import { TDepositTransaction } from "../types/transaction";
import {
  formatNumber,
  formatShortAddress,
  formatUnixTimestamp,
} from "../utils/format";
import { useSelector } from "react-redux";
import { RootState } from "../store/index";
import { TNetwork } from "../types/network";
import { BROADCAST_EVENTS } from "../libs/broadcast";
import Link from "next/link";

const EColorStatus = {
  pending: "orange",
  processing: "orange",
  completed: "green",
  failed: "red",
};

const DepositItem = ({ depositTx }: { depositTx: TDepositTransaction }) => {
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
  const networks = useSelector((state: RootState) => state.metadata.networks);
  const assets = useSelector((state: RootState) => state.metadata.assets);

  const assetData = useMemo(
    () => assets.find((item) => item.symbol === depositTx?.asset),
    [assets, depositTx]
  );

  const networkDeposit = networks.find(
    (network: TNetwork) => network.id === depositTx.networkId
  );

  if (isMobile) {
    return (
      <div className="border-white-50 flex flex-col gap-4 border-b py-2">
        <div className="flex justify-between">
          <div className="body-md-medium-14 flex items-center gap-2">
            <img
              src={assetData?.logoUrl}
              alt={depositTx.asset}
              width={20}
              height={20}
              className="aspect-square h-[20px] w-[20px] rounded-full"
            />
            {depositTx.asset}
          </div>
          <div className="body-md-medium-14">
            {formatNumber(depositTx.amount)} {depositTx.asset}
          </div>
        </div>
        <div className="flex justify-between">
          <div className="body-md-regular-14 text-white-500">Status</div>
          <div
            className={`body-md-regular-14 text-${
              EColorStatus[depositTx.status as keyof typeof EColorStatus]
            }-500`}
          >
            {depositTx.status}
          </div>
        </div>
        <div className="flex justify-between">
          <div className="body-md-regular-14 text-white-500">Time</div>
          <div className="body-md-regular-14">
            {formatUnixTimestamp(depositTx.createdAt, "YYYY-MM-DD HH:mm")}
          </div>
        </div>
        <div className="flex justify-between">
          <div className="body-md-regular-14 text-white-500">Network</div>
          <div className="body-md-regular-14">{networkDeposit?.symbol}</div>
        </div>
        <div className="flex justify-between">
          <div className="body-md-regular-14 text-white-500">Address</div>
          <div className="body-md-regular-14">
            <div className="body-sm-regular-12 flex items-center gap-2.5">
              <div>{formatShortAddress(depositTx.address)}</div>
              <a
                href={`${networkDeposit?.explorerUrl}/address/${depositTx.address}`}
                target="_blank"
              >
                <LinkAngledIcon className="cursor-pointer" />
              </a>
              <CopyIcon
                className="cursor-pointer"
                onClick={() => copyToClipboard(depositTx.address)}
              />
            </div>
          </div>
        </div>
        <div className="flex justify-between">
          <div className="body-md-regular-14 text-white-500">
            Transaction ID (TxID)
          </div>
          <div className="body-md-regular-14">
            <div className="body-sm-regular-12 flex items-center gap-2.5">
              {depositTx.txHash ? (
                <>
                  {" "}
                  <div>{formatShortAddress(depositTx.txHash)}</div>
                  <a
                    href={`${networkDeposit?.explorerUrl}/tx/${depositTx.txHash}`}
                    target="_blank"
                  >
                    <LinkAngledIcon className="cursor-pointer" />
                  </a>
                  <CopyIcon
                    className="cursor-pointer"
                    onClick={() => copyToClipboard(depositTx.txHash)}
                  />
                </>
              ) : (
                "--"
              )}
            </div>
          </div>
        </div>
        <div className="flex justify-between">
          <div className="body-md-regular-14 text-white-500">
            Deposit to wallet
          </div>
          <div className="body-md-regular-14">Spot Wallet</div>
        </div>
      </div>
    );
  }
  return (
    <div className="border-white-50 hover:bg-white-50 flex w-full items-center lg:border-b">
      <div className="body-sm-regular-12 flex w-[20%] items-center gap-2 px-2 py-2.5 text-left lg:min-w-[184px]">
        <img
          src={assetData?.logoUrl}
          alt={depositTx.asset}
          width={24}
          height={24}
          className="aspect-square h-[24px] w-[24px] rounded-full"
        />
        <div>
          <div className="body-sm-regular-12">{depositTx.asset}</div>
          <div className="body-xs-regular-10 text-white-500">
            {formatUnixTimestamp(depositTx.createdAt, "YYYY-MM-DD HH:mm")}
          </div>
        </div>
      </div>
      <div className="body-sm-regular-12 w-[10%] px-2 py-2.5 text-right lg:min-w-[100px]">
        {formatNumber(depositTx.amount)}
      </div>
      <div className="body-sm-regular-12 w-[10%] px-2 py-2.5 text-right lg:min-w-[100px]">
        {networkDeposit?.symbol}
      </div>
      <div className="flex justify-end px-2 py-2.5 md:min-w-[184px] lg:w-[20%]">
        <div className="body-sm-regular-12 flex items-center gap-2.5">
          <div>{formatShortAddress(depositTx.address)}</div>
          <a
            href={`${networkDeposit?.explorerUrl}/address/${depositTx.address}`}
            target="_blank"
          >
            <LinkAngledIcon className="cursor-pointer" />
          </a>
          <CopyIcon
            className="cursor-pointer"
            onClick={() => copyToClipboard(depositTx.address)}
          />
        </div>
      </div>
      <div className="flex justify-end px-2 py-2.5 md:min-w-[184px] lg:w-[20%] ">
        <div className="body-sm-regular-12 flex items-center gap-2.5">
          {depositTx.txHash ? (
            <>
              {" "}
              <div>{formatShortAddress(depositTx.txHash)}</div>
              <a
                href={`${networkDeposit?.explorerUrl}/tx/${depositTx.txHash}`}
                target="_blank"
              >
                <LinkAngledIcon className="cursor-pointer" />
              </a>
              <CopyIcon
                className="cursor-pointer"
                onClick={() => copyToClipboard(depositTx.txHash)}
              />
            </>
          ) : (
            "--"
          )}
        </div>
      </div>
      <div className="body-sm-regular-12 w-[15%] px-2 py-2.5 text-right lg:min-w-[150px]">
        Spot Wallet
      </div>
      <div
        className={`body-sm-regular-12 w-[15%] px-2 py-2.5 text-right capitalize text-${
          EColorStatus[
            depositTx.status?.toLowerCase() as keyof typeof EColorStatus
          ]
        }-500 lg:min-w-[150px]`}
      >
        {depositTx.status?.toLowerCase()}
      </div>
    </div>
  );
};

export const TableRecentDeposit = () => {
  const dataTableRef = useRef<HTMLDivElement | null>(null);
  const [isMounted, setIsMounted] = useState<boolean>(false);
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  const getData = async (params: any) => {
    try {
      const result = await rf
        .getRequest("AccountRequest")
        .getDepositTransactions({
          ...params,
          isExternal: true,
        });
      return {
        cursor: result?.cursor,
        data: result?.docs || [],
      };
    } catch (err) {
      console.log(err, "getDataDepositTx Error");
      return { cursor: null, data: [] };
    }
  };

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) return <></>;

  return (
    <div className="border-white-100 p-4 lg:rounded-[16px] lg:border">
      <div className="mb-2 flex items-center justify-between">
        <div className="lg:text-white-500 heading-sm-medium-16">
          Recent Deposits
        </div>
        <Link href="/my/transaction-history">
          <div className="body-sm-medium-12 flex cursor-pointer items-center gap-2">
            More <ChevronDownIcon className="rotate-[-90deg]" />
          </div>
        </Link>
      </div>

      <div className="w-full">
        <AppDataTableRealtime
          minWidth={1300}
          ref={dataTableRef}
          getData={getData as any}
          shouldAutoFetchOnInit
          overrideBodyClassName="w-full"
          handleAddNewItem={{
            broadcastName: BROADCAST_EVENTS.NEW_TRANSACTION,
            fieldKey: "id",
            formatter: (data: any) => {
              const newData = JSON.parse(data) as any;
              if (newData?.transactionType !== "Deposit") return null;
              return {
                address: newData.address,
                amount: newData.amount,
                asset: newData.asset,
                createdAt: newData.timestamp,
                fee: newData.fee,
                id: newData.id,
                networkId: newData?.networkId,
                status: newData?.status || "PENDING",
                txHash: newData?.txHash,
                updatedAt: newData?.timestamp,
                userId: newData?.userId,
              };
            },
          }}
          handleUpdateItem={[
            {
              broadcastName: BROADCAST_EVENTS.UPDATED_TRANSACTION,
              fieldKey: "id",
              formatter: (data: any) => {
                const newData = JSON.parse(data);
                if (newData?.transactionType !== "Deposit") return null;
                return newData;
              },
              update: (oldData: any, newData: any) => {
                return {
                  ...oldData,
                  status: newData.status,
                  txHash: newData?.txHash,
                };
              },
            },
          ]}
          renderHeader={() => {
            if (isMobile) return null;
            return (
              <>
                <div className="flex w-full items-center">
                  <div className="body-sm-regular-12 text-white-500 flex w-[20%] items-center px-2 py-1.5 md:min-w-[184px] ">
                    Asset/Date
                  </div>
                  <div className="body-sm-regular-12 text-white-500 w-[10%] px-2 py-1.5 text-right md:min-w-[100px] ">
                    Amount
                  </div>
                  <div className="body-sm-regular-12 text-white-500 w-[10%] px-2 py-1.5 text-right md:min-w-[100px]">
                    Network
                  </div>
                  <div className="body-sm-regular-12 text-white-500 w-[20%] px-2 py-1.5 text-right md:min-w-[184px]">
                    Address
                  </div>
                  <div className="body-sm-regular-12 text-white-500 w-[20%] px-2 py-1.5 text-right md:min-w-[184px]">
                    TxID
                  </div>
                  <div className="body-sm-regular-12 text-white-500 w-[15%] px-2 py-1.5 text-right md:min-w-[150px]">
                    Deposit wallet
                  </div>
                  <div className="body-sm-regular-12 text-white-500 w-[15%] px-2 py-1.5 text-right md:min-w-[150px]">
                    Status
                  </div>
                </div>
              </>
            );
          }}
          renderRow={(item: TDepositTransaction, index: number) => {
            return <DepositItem key={index} depositTx={item} />;
          }}
          height={450}
        />
      </div>
    </div>
  );
};
