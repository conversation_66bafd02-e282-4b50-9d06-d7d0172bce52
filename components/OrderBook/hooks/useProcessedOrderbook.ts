import { useMemo, useRef } from "react";
import { OrderBookLevel } from "@/types/OrderBook";
import { multipliedBN, plusBN } from "@/utils/number";
import BigNumber from "bignumber.js";
import useOrderbookThrottle from "./useOrderbookThrottle";

export const useProcessedOrderbook = (
  orderbook: OrderBookLevel[],
  decimal: number,
  displayItemNumber: number,
  isAsk = false
) => {
  const largestAmountRef = useRef("0");

  const throttledValue = useOrderbookThrottle(orderbook, 200);

  return useMemo(() => {
    if (!throttledValue?.length) {
      return { orderbookData: [], largestAmount: "0" };
    }

    const groupOrderbookByDecimal = () => {
      const grouped: Record<string, string> = {};

      for (const [price, amount] of throttledValue) {
        const bucket = BigNumber(price)
          .dividedBy(decimal)
          .integerValue(BigNumber.ROUND_FLOOR)
          .multipliedBy(decimal)
          .toFixed();

        if (!grouped[bucket]) {
          grouped[bucket] = "0";
        }
        grouped[bucket] = plusBN(grouped[bucket], amount);
      }

      if (isAsk) {
        return Object.entries(grouped).sort(
          (a, b) => BigNumber(a[0]).minus(b[0]).toNumber() // desc
        );
      }

      return Object.entries(grouped).sort(
        (a, b) => BigNumber(b[0]).minus(a[0]).toNumber() // asc
      );
    };

    let _largestAmount = "0";
    let cumulativeSum = "0";
    const orderbookData = [];
    const orderbookWithDecimals = groupOrderbookByDecimal();

    for (const [price, quantity] of orderbookWithDecimals.slice(
      0,
      displayItemNumber
    )) {
      // Calculate total only once
      const total = multipliedBN(price, quantity);

      cumulativeSum = BigNumber(cumulativeSum).plus(total).toString();

      // Track largest amount
      if (BigNumber(total).isGreaterThan(_largestAmount)) {
        _largestAmount = total;
      }

      orderbookData.push({
        price,
        amount: quantity,
        total,
        cumulative: cumulativeSum,
      });
    }

    // Update ref with new largest amount
    largestAmountRef.current = _largestAmount;

    return {
      orderbookData: orderbookData,
      largestAmount: _largestAmount,
    };
  }, [throttledValue, decimal, displayItemNumber]);
};
