import React, { forwardRef } from "react";
import { BaseModal } from "@/modals/BaseModal";
import { RESOLUTIONS } from "../hooks/useHistoryFilter";
import DatePicker from "react-datepicker";
import { CalendarIcon } from "@/assets/icons";
import { AppButton } from "@/components";
import moment from "moment";

export const CustomDateInput = forwardRef<
  HTMLDivElement,
  { value?: any; onClick?: () => void }
>(({ value, onClick }, ref) => {
  return (
    <div
      onClick={onClick}
      ref={ref}
      className={`text-white-900 border-white-100 body-md-medium-14 flex w-[calc(100vw-32px)] cursor-pointer items-center justify-between gap-1 rounded-[4px] rounded-[6px] border p-2`}
    >
      {value ? value : <span className="text-white-300">Select date</span>}
      <CalendarIcon />
    </div>
  );
});

export const ModalFilterByTime = ({
  isOpen,
  onClose,
  resolution,
  setResolution,
  startDate,
  endDate,
  setDateRange,
}: {
  isOpen: boolean;
  onClose: () => void;
  resolution: string;
  setResolution: (value: string) => void;
  startDate: Date | undefined;
  endDate: Date | undefined;
  setDateRange: (value: [Date, Date]) => void;
}) => {
  const onReset = () => {
    setResolution("1w");
    setDateRange([moment().subtract(7, "days").toDate(), moment().toDate()]);
    onClose();
  };

  return (
    <BaseModal
      className="w-screen md:w-[500px]"
      isOpen={isOpen}
      onClose={onClose}
      isBottom
    >
      <div className="min-h-[200px]">
        <div className="heading-sm-medium-16 mb-4">Filter</div>

        <div className="grid grid-cols-4 gap-2">
          {RESOLUTIONS.map((item: any, index: number) => {
            return (
              <div
                onClick={() => {
                  setResolution(item.value);
                  onClose();
                }}
                key={index}
                className={`body-sm-medium-12 flex items-center justify-center rounded-[6px] border p-2 ${
                  resolution === item.value
                    ? "border-green-800 text-green-500"
                    : "border-white-150"
                }`}
              >
                {item.name}
              </div>
            );
          })}
        </div>

        <div className="mt-4 w-full">
          <DatePicker
            selectsRange
            startDate={startDate}
            endDate={endDate}
            onChange={(update: any) => setDateRange(update)}
            placeholderText="YYYY/MM/DD - YYYY/MM/DD"
            dateFormat="YYYY/MM/dd"
            customInput={<CustomDateInput />}
            className="!w-full rounded border p-2"
          />
        </div>

        <div className="mt-8 grid grid-cols-2 gap-3">
          <AppButton variant="secondary" size="large" onClick={onReset}>
            Reset
          </AppButton>
          <AppButton variant="buy" size="large" onClick={onClose}>
            Confirm
          </AppButton>
        </div>
      </div>
    </BaseModal>
  );
};
