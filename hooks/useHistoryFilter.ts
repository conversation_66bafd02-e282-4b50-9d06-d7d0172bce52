import { useState, useMemo, useEffect, useCallback } from "react";
import moment from "moment";
import { EOrderSideParam } from "@/components/OrderHistory";
import { EOrderTypeParam } from "@/components/OrderExchange/components/SelectOrderType";

export interface HistoryFilterConfig {
  includeQuoteAsset?: boolean;
  includeOrderType?: boolean;
  includeSortBy?: boolean;
}

export interface HistoryFilterState {
  // Date filters
  resolution: string;
  dateRange: [Date, Date];
  startDate: Date | undefined;
  endDate: Date | undefined;

  // Asset filters
  base: string;
  quote: string;

  // Order filters
  side: string;
  orderType: string;
  sortBy: string;

  // UI state
  isShowModalFilter: boolean;
}

export interface HistoryFilterActions {
  setResolution: (value: string) => void;
  setDateRange: (value: [Date, Date]) => void;
  setBase: (value: string) => void;
  setQuote: (value: string) => void;
  setSide: (value: string) => void;
  setOrderType: (value: string) => void;
  setSortBy: (value: string) => void;
  setIsShowModalFilter: (value: boolean) => void;
  handleResetFilters: () => void;
}

export const RESOLUTIONS = [
  {
    name: "1 Day",
    value: "1d",
  },
  {
    name: "1 Week",
    value: "1w",
  },
  {
    name: "1 Month",
    value: "1m",
  },
  {
    name: "3 Month",
    value: "3m",
  },
];

export const useHistoryFilter = (config: HistoryFilterConfig = {}) => {
  const {
    includeQuoteAsset = false,
    includeOrderType = false,
    includeSortBy = false,
  } = config;

  // Date filters
  const [resolution, setResolution] = useState<string>("1w");
  const [dateRange, setDateRange] = useState<[Date, Date]>([] as any);
  const [startDate, endDate] = useMemo(() => {
    return dateRange;
  }, [dateRange]);

  // Asset filters
  const [base, setBase] = useState<string>("");
  const [quote, setQuote] = useState<string>("");

  // Order filters
  const [side, setSide] = useState<string>(EOrderSideParam.All);
  const [orderType, setOrderType] = useState<string>(EOrderTypeParam.All);
  const [sortBy, setSortBy] = useState<string>("createdAt");

  // UI state
  const [isShowModalFilter, setIsShowModalFilter] = useState<boolean>(false);

  // Update date range when resolution changes
  useEffect(() => {
    if (resolution === "3m") {
      setDateRange([
        moment().subtract(3, "months").toDate(),
        moment().toDate(),
      ]);
    }
    if (resolution === "1m") {
      setDateRange([
        moment().subtract(1, "months").toDate(),
        moment().toDate(),
      ]);
    }
    if (resolution === "1w") {
      setDateRange([moment().subtract(7, "days").toDate(), moment().toDate()]);
    }
    if (resolution === "1d") {
      setDateRange([moment().subtract(1, "days").toDate(), moment().toDate()]);
    }
  }, [resolution]);

  const handleResetFilters = useCallback(() => {
    setResolution("1w");
    setSide(EOrderSideParam.All);
    setBase("");
    if (includeQuoteAsset) {
      setQuote("");
    }
    if (includeOrderType) {
      setOrderType(EOrderTypeParam.All);
    }
    if (includeSortBy) {
      setSortBy("createdAt");
    }
    setDateRange([moment().subtract(7, "days").toDate(), moment().toDate()]);
    // Date range will be reset by useEffect when resolution changes to "1d"
  }, [includeQuoteAsset, includeOrderType, includeSortBy]);

  const state: HistoryFilterState = {
    resolution,
    dateRange,
    startDate,
    endDate,
    base,
    quote,
    side,
    orderType,
    sortBy,
    isShowModalFilter,
  };

  const actions: HistoryFilterActions = {
    setResolution,
    setDateRange,
    setBase,
    setQuote,
    setSide,
    setOrderType,
    setSortBy,
    setIsShowModalFilter,
    handleResetFilters,
  };

  return { state, actions };
};
