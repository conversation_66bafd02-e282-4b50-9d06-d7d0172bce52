export interface PasswordValidation {
  isValidLength: boolean;
  isValidHasNumber: boolean;
  isValidHasUppercase: boolean;
  isValid: boolean;
}

export interface PasswordMatchValidation {
  isPasswordsMatch: boolean;
  isConfirmPasswordValid: boolean;
}

/**
 * Validates password according to the application's password policy
 * @param password - The password to validate
 * @returns PasswordValidation object with validation results
 */
export const validatePassword = (password: string): PasswordValidation => {
  const isValidLength = password.length > 7 && password.length < 129;
  const isValidHasNumber = /\d/.test(password);
  const isValidHasUppercase = /[A-Z]/.test(password);
  
  return {
    isValidLength,
    isValidHasNumber,
    isValidHasUppercase,
    isValid: isValidLength && isValidHasNumber && isValidHasUppercase,
  };
};

/**
 * Validates password confirmation matching
 * @param password - The original password
 * @param confirmPassword - The confirmation password
 * @returns PasswordMatchValidation object with matching results
 */
export const validatePasswordMatch = (
  password: string,
  confirmPassword: string
): PasswordMatchValidation => {
  const isPasswordsMatch = password === confirmPassword && confirmPassword.length > 0;
  const isConfirmPasswordValid = confirmPassword.length > 0;
  
  return {
    isPasswordsMatch,
    isConfirmPasswordValid,
  };
};

/**
 * Validates complete password form (password + confirmation)
 * @param password - The password to validate
 * @param confirmPassword - The confirmation password (optional)
 * @returns Combined validation results
 */
export const validatePasswordForm = (
  password: string,
  confirmPassword?: string
) => {
  const passwordValidation = validatePassword(password);
  
  if (confirmPassword !== undefined) {
    const matchValidation = validatePasswordMatch(password, confirmPassword);
    return {
      ...passwordValidation,
      ...matchValidation,
      isFormValid: passwordValidation.isValid && matchValidation.isPasswordsMatch,
    };
  }
  
  return {
    ...passwordValidation,
    isFormValid: passwordValidation.isValid,
  };
};
